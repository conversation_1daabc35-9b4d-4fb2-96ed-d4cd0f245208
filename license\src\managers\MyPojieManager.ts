import { MyAESCBC } from '../crypto/MyAESCBC';
import * as base32 from 'hi-base32';

export class MyPojieManager {
  private readonly aes: MyAESCBC;

  constructor() {
    this.aes = new MyAESCBC("9B8FD68A366F4D03", "305FB72D83134CA0");
  }

  /**
   * 获取激活码
   * @param softwareName 软件名称
   * @param machineCode 机器码
   * @param permissionId 权限ID
   * @param startTs 开始时间戳（可选）
   * @param endTs 结束时间戳（可选）
   * @param activeEndTs 激活码有效期（可选）
   * @returns 激活码
   */
  public async getActiveCode(
    softwareName: string,
    machineCode: string,
    permissionId: string,
    startTs?: number,
    endTs?: number,
    activeEndTs?: number
  ): Promise<string> {
    const info = {
      software: softwareName,
      code: machineCode,
      permissionId: permissionId,
      startTs: startTs?.toString() || "0",
      endTs: endTs?.toString() || "4742092800", // 2120-03-17
      active_endTs: activeEndTs?.toString() || "",
    };

    const encrypted = this.aes.encrypt(JSON.stringify(info));
    return base32.encode(encrypted);
  }
}
