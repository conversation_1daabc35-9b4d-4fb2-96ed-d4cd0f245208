<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>激活码生成器</title>
  <style>
    :root {
      --primary-color: #2196F3;
      --secondary-color: #1976D2;
      --background-color: #E3F2FD;
      --card-background: #ffffff;
      --text-color: #333333;
      --border-color: #e0e0e0;
      --success-color: #4CAF50;
      --error-color: #f44336;
      --hover-color: #BBDEFB;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background-color: var(--background-color);
      color: var(--text-color);
      line-height: 1.5;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .container {
      width: 100%;
      max-width: 1000px;
      padding: 20px;
    }

    .content {
      background-color: var(--card-background);
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 32px;
      position: relative;
    }

    .section-title {
      font-size: 28px;
      color: var(--primary-color);
      margin-bottom: 32px;
      text-align: center;
      font-weight: 600;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
      margin-top: 0;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-color);
      font-size: 15px;
    }

    select, textarea {
      width: 100%;
      padding: 10px 14px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      font-size: 15px;
      transition: all 0.3s ease;
      background-color: #fff;
    }

    select:hover, textarea:hover {
      border-color: var(--primary-color);
      background-color: var(--hover-color);
    }

    select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }

    textarea {
      height: 70px;
      resize: none;
      font-family: 'Consolas', monospace;
    }

    #machine-code {
      height: 40px;
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      line-height: 40px;
      padding: 0 14px;
    }

    #license-display {
      height: 90px;
      background-color: #f8f9fa;
    }

    .button-group {
      display: flex;
      gap: 16px;
      margin-top: 32px;
    }

    button {
      flex: 1;
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    #generate-btn {
      background-color: var(--primary-color);
      color: white;
    }

    #generate-btn:hover {
      background-color: var(--secondary-color);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
    }

    #copy-btn {
      background-color: #e0e0e0;
      color: var(--text-color);
    }

    #copy-btn:hover {
      background-color: #d0d0d0;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .alert {
      position: absolute;
      top: 32px;
      left: 32px;
      right: 32px;
      padding: 14px;
      border-radius: 6px;
      font-size: 15px;
      display: none;
      animation: fadeIn 0.3s ease;
      z-index: 1000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .alert::before {
      content: '';
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    .alert-success {
      background-color: #e8f5e9;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }

    .alert-success::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232e7d32'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
    }

    .alert-error {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }

    .alert-error::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23c62828'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
    }

    .alert-info {
      background-color: #e3f2fd;
      color: #1565c0;
      border: 1px solid #bbdefb;
    }

    .alert-info::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231565c0'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'/%3E%3C/svg%3E");
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeOut {
      from { opacity: 1; transform: translateY(0); }
      to { opacity: 0; transform: translateY(-20px); }
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
    }

    select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 14px center;
      padding-right: 40px;
    }

    select:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
      opacity: 0.7;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="content">
      <h1 class="section-title">激活码生成器</h1>
      
      <div class="form-group">
        <label for="software-name">软件名称</label>
        <select id="software-name">
          <option value="SEATool">SEATool</option>
          <option value="WaveAnalysis">WaveAnalysis</option>
        </select>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="permission-id">权限ID</label>
          <select id="permission-id">
            <option value="1" selected>1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
          </select>
          </div>

        <div class="form-group">
          <label for="license-type">激活码类型</label>
          <select id="license-type">
            <option value="永久激活码">永久激活码</option>
            <option value="临时激活码">临时激活码（指定过期时间）</option>
            <option value="限时激活的临时激活码">限时激活的临时激活码（10分钟内有效）</option>
          </select>
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label for="machine-code">机器码</label>
          <textarea id="machine-code"></textarea>
        </div>

        <div class="form-group">
          <label for="valid-days">有效天数</label>
          <select id="valid-days" disabled>
            <option value="3">3天</option>
            <option value="7">7天</option>
            <option value="15">15天</option>
            <option value="30">30天</option>
            <option value="90">90天</option>
            <option value="180">180天</option>
            <option value="365">365天</option>
          </select>
        </div>
      </div>

      <div class="form-group">
        <label for="license-display">生成的激活码</label>
        <textarea id="license-display" readonly></textarea>
      </div>

      <div class="button-group">
        <button id="generate-btn">生成激活码</button>
        <button id="copy-btn">复制激活码</button>
      </div>
<div id="alert" class="alert"></div>
    </div>
  </div>

  <script src="renderer.js"></script>
</body>
</html>
