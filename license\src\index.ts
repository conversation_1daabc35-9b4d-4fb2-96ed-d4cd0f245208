import { ErrorCode } from './errors/ErrorCode';

// 错误码枚举
export { ErrorCode } from './errors/ErrorCode';

// 错误信息映射
export { ErrorInfo } from './errors/ErrorInfo';

// 机器码生成器
export { MachineCodeGenerator } from './core/MachineCodeGenerator';

// AES加密工具
export { MyAESCBC } from './crypto/MyAESCBC';

// 授权管理器基类
export { BaseAuthManager, TimeResult } from './core/BaseAuthManager';

// 授权管理器实现
export { MyAuthManager, myAuthMgr } from './managers/MyAuthManager';

// 激活码生成工具
export { MyPojieManager } from './managers/MyPojieManager';

// 类型定义
export interface AuthResult {
  errCode: ErrorCode;
  errInfo: string;
}

export interface AuthData {
  code: string;
  startTs: number;
  endTs: number;
  active_endTs?: number;
}
