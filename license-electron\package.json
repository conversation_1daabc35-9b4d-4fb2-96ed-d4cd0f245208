{"name": "license-electron", "version": "1.0.2", "description": "License Generator Electron App", "main": "main.js", "scripts": {"start": "chcp 65001 && electron .", "test": "echo \"Error: no test specified\" && exit 1", "build": "electron-builder build --win --x64", "package": "npm run build"}, "build": {"appId": "com.license.app", "productName": "License Generator", "directories": {"output": "dist"}, "win": {"target": ["zip"], "icon": "build/icon.ico"}, "files": ["**/*", "../license/dist/**/*"], "extraResources": [{"from": "../license/dist", "to": "license/dist", "filter": ["**/*"]}]}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@electron/remote": "^2.1.2", "electron": "^36.2.0", "electron-builder": "^24.13.3"}, "dependencies": {"license": "file:../license/license-1.0.4.tgz"}}