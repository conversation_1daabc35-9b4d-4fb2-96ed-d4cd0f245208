/**
 * 时间获取结果接口
 */
export interface TimeResult {
  timestamp: number;
  isNetworkTime: boolean;
  localTime: number;
  serverUsed?: string;
  retryCount?: number;
}

/**
 * 授权管理器基类
 * 定义授权管理的基本接口和方法
 */
export abstract class BaseAuthManager {
  /**
   * 获取当前时间戳
   * @returns 时间获取结果对象
   */
  protected abstract getCurrentTimestamp(): Promise<TimeResult>;

  /**
   * 获取机器码
   * @returns 机器码
   */
  public abstract getMachineCode(): Promise<string>;

  /**
   * 检查授权状态
   * @param softwareName 软件名称
   * @returns 授权检查结果
   */
  public abstract checkAuth(
    softwareName: string
  ): Promise<{ errCode: number; errInfo: string }>;

  /**
   * 激活授权
   * @param softwareName 软件名称
   * @param activeCode 激活码
   * @returns 激活结果
   */
  public abstract activate(
    softwareName: string,
    activeCode: string
  ): Promise<{ errCode: number; errInfo: string }>;
}
